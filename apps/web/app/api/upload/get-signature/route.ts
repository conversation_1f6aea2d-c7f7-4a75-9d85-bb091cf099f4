import { NextRequest, NextResponse } from 'next/server'
import OSS from 'ali-oss'
import { v4 as uuidv4 } from 'uuid'
// 

const OAAconfig = {
  accessKeyId: process.env.ALIYUN_OSS_ACCESS_KEY_ID || '',
  accessKeySecret: process.env.ALIYUN_OSS_ACCESS_KEY_SECRET || '',
  region: process.env.ALIYUN_OSS_REGION || '',
  bucket: process.env.ALIYUN_OSS_BUCKET || '',
  endpoint: process.env.ALIYUN_OSS_ENDPOINT || '',
  secure: true, // 强制启用 HTTPS
}

// OSS 客户端配置
const ossClient = new OSS(OAAconfig)

/**
 * 处理 CORS 预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}

/**
 * 生成阿里云 OSS 签名 URL，用于客户端直接上传
 *
 * 请求参数:
 * - filename: 文件名
 * - contentType: 文件类型
 *
 * 返回:
 * - url: 签名 URL
 * - ossUrl: 上传后的文件 URL
 * - formData: 上传所需的表单数据
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求参数
    const { filename, contentType } = await request.json()

    if (!filename || !contentType) {
      return NextResponse.json(
        { code: 400, msg: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!contentType.startsWith('image/')) {
      return NextResponse.json(
        { code: 400, msg: 'Only image files are supported' },
        { status: 400 }
      )
    }

    // 生成唯一的文件名
    const ext = filename.split('.').pop()
    const objectKey = `uploads/${uuidv4()}.${ext}`

    // 生成签名 URL
    const options = {
      expires: 60, // URL 有效期（秒）
      method: 'PUT' as const, // 使用类型断言确保类型兼容
      'Content-Type': contentType,
    }

    // 生成签名 URL
    const signedUrl = ossClient.signatureUrl(objectKey, options)

    // 计算上传后的文件 URL
    // 使用阿里云 OSS 的标准域名格式构建 URL
    const endpoint = process.env.ALIYUN_OSS_ENDPOINT || ''
    const bucket = OAAconfig.bucket
    const region = OAAconfig.region

    let ossUrl
    if (endpoint) {
      // 如果有自定义 endpoint，使用它
      const cleanEndpoint = endpoint
        .replace(/^https?:\/\//, '')
        .replace(/\/$/, '')
      ossUrl = `https://${bucket}.${cleanEndpoint}/${objectKey}`
    } else {
      // 否则使用标准 OSS URL 格式
      ossUrl = `https://${bucket}.${region}.aliyuncs.com/${objectKey}`
    }

    // 返回签名 URL 和上传后的文件 URL
    const response = NextResponse.json({
      code: 200,
      msg: 'Signature generated successfully',
      data: {
        url: signedUrl,
        ossUrl: ossUrl,
      },
    })

    // 添加 CORS 头
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set(
      'Access-Control-Allow-Methods',
      'GET, POST, PUT, DELETE, OPTIONS'
    )
    response.headers.set(
      'Access-Control-Allow-Headers',
      'Content-Type, Authorization'
    )

    return response
  } catch (error) {
    console.error('Failed to generate signature:', error)
    const errorResponse = NextResponse.json(
      { code: 500, msg: 'Failed to generate signature' },
      { status: 500 }
    )

    // 添加 CORS 头
    errorResponse.headers.set('Access-Control-Allow-Origin', '*')
    errorResponse.headers.set(
      'Access-Control-Allow-Methods',
      'GET, POST, PUT, DELETE, OPTIONS'
    )
    errorResponse.headers.set(
      'Access-Control-Allow-Headers',
      'Content-Type, Authorization'
    )

    return errorResponse
  }
}
