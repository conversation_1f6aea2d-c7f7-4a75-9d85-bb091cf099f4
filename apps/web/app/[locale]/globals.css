@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  body[data-scroll-locked] {
    overflow-y: auto !important;
  }
}

pre.shiki {
  @apply mb-4 rounded-lg p-6;
}

@keyframes shine-slow {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes shine-fast {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100%) translateX(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100%) translateX(20px);
    opacity: 0;
  }
}

.animate-shine-slow {
  animation: shine-slow 3s infinite;
}

.animate-shine-fast {
  animation: shine-fast 2s infinite;
}

.animate-glow {
  animation: glow 2s infinite;
}

.particles-container .particle {
  position: absolute;
  bottom: 0;
  left: 0;
}

.particles-container .particle:nth-child(1) {
  left: 10%;
}
.particles-container .particle:nth-child(2) {
  left: 30%;
}
.particles-container .particle:nth-child(3) {
  left: 50%;
}
.particles-container .particle:nth-child(4) {
  left: 70%;
}
.particles-container .particle:nth-child(5) {
  left: 90%;
}

@keyframes loadingText {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.animate-loading {
  animation: loadingText 1.5s infinite;
}

/* 在您的 CSS 文件中添加以下内容 */
@keyframes floating {
  0% {
    transform: translateY(0) rotate(0deg);
    background-position: 0% 50%;
  }
  25% {
    transform: translateY(-3px) rotate(0.3deg);
    background-position: 50% 50%;
  }
  50% {
    transform: translateY(0) rotate(0deg);
    background-position: 100% 50%;
  }
  75% {
    transform: translateY(3px) rotate(-0.3deg);
    background-position: 50% 50%;
  }
  100% {
    transform: translateY(0) rotate(0deg);
    background-position: 0% 50%;
  }
}

.animate-floating {
  animation: floating 4s ease-in-out infinite;
  background-size: 200% auto;
}

/* app/globals.css 或者其他全局CSS文件 */
@keyframes blob {
  0% {
    transform: scale(1);
  }
  33% {
    transform: scale(1.1) translateY(-10px);
  }
  66% {
    transform: scale(0.9) translateX(10px);
  }
  100% {
    transform: scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite alternate;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Hover放大效果容器 - 防止元素超出父容器 */
.hover-scale-container {
  padding: 1rem; /* 为放大效果提供空间 */
}

.hover-scale-container > * {
  transition: transform 0.3s ease;
}

.hover-scale-container > *:hover {
  transform: scale(1.05);
}

/* 更小的放大效果 */
.hover-scale-container-sm {
  padding: 0.5rem;
}

.hover-scale-container-sm > * {
  transition: transform 0.3s ease;
}

.hover-scale-container-sm > *:hover {
  transform: scale(1.02);
}

/* 渐变动画效果 */
@keyframes gradient-xy {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

/* 更大的放大效果 */
.hover-scale-container-lg {
  padding: 1.5rem;
}

.hover-scale-container-lg > * {
  transition: transform 0.3s ease;
}

.hover-scale-container-lg > *:hover {
  transform: scale(1.1);
}

/* 图片hover放大效果 */
.image-hover-scale {
  transition: transform 0.3s ease;
  overflow: hidden;
}

.image-hover-scale:hover {
  transform: scale(1.05);
}

.image-hover-scale img {
  transition: transform 0.3s ease;
}

.image-hover-scale:hover img {
  transform: scale(1.1);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #c9c9c9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c9c9c9;
}
