/* 流动渐变边框动画 - 升级为高光流动效果 */
.animated-gradient-border {
  position: relative;
  z-index: 1;
  border-radius: 1.5rem;
  background: linear-gradient(270deg, #a21caf, #6366f1, #ec4899, #a21caf);
  background-size: 600% 600%;
  animation: borderFlow 6s linear infinite;
  padding: 3px; /* 边框宽度 */
  /* 关键：双层背景裁切 */
  background-clip: border-box;
}

.animated-gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 3px; /* 边框宽度 */
  border-radius: 1.5rem; /* 保持和内容一致 */
  background: linear-gradient(270deg, #a21caf, #6366f1, #ec4899, #a21caf);
  background-size: 600% 600%;
  animation: borderFlow 6s linear infinite;
  z-index: 2;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

@keyframes borderFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.animated-gradient-border > div {
  border-radius: 1rem;
  background: #18181bcc; /* 深色半透明背景 */
  width: 100%;
  height: 100%;
  background-clip: padding-box;
}

.custom-scrollbar::-webkit-scrollbar {
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(35, 32, 58, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(
    90deg,
    rgba(139, 92, 246, 0.6),
    rgba(236, 72, 153, 0.6)
  );
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    90deg,
    rgba(139, 92, 246, 0.8),
    rgba(236, 72, 153, 0.8)
  );
}

/* 隐藏滚动条样式 */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
