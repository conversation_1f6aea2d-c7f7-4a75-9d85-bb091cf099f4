const fs = require('fs');
const path = require('path');

const translations = {
  en: {
    oldfilter: {
      // page.tsx
      title: "AI Age Progression Free Online | Try Aging Filter to See Your Future Face",
      description: "See how you'll look when you're older with our AI age progression free online tool. Try our realistic age filter and aging filter in seconds — free, no signup, no watermark.",
      keywords: "AI age progression, AI age progression free online, age filter, aging filter, age filter free online, aging filter free, photo aged, old filter, old age filter, face ager, aging ai, photo aging, age a photo, face aging online free, old man filter, old person filter, age changer, AI old, face old, what will I look like when I'm older",
      openGraphTitle: "AI Age Progression Free Online | Try Aging Filter to See Your Future Face",
      openGraphDescription: "See how you'll look when you're older with our AI age progression free online tool. Try our realistic age filter and aging filter in seconds — free, no signup, no watermark.",
      twitterTitle: "AI Age Progression Free Online | Try Aging Filter to See Your Future Face",
      twitterDescription: "See how you'll look when you're older with our AI age progression free online tool. Try our realistic age filter and aging filter in seconds — free, no signup, no watermark.",
      pageTitle: "AI Age Progression Free Online – Instantly See Your Future Face",
      pageDescription: "See how you'll look as you age with our AI age progression free online tool. Upload a photo and get a realistic photo aged result in seconds using our advanced AI age filter and aging filter.",
      uploadButton: "Upload and Try Age Filter Now",
      // WhyUs.tsx
      whyUsTitle: "Why Choose Our Age Progression Tool?",
      // TestimonialSection.tsx
      testimonialTitle: "What Our Users Say",
      testimonialDescription: "Thousands have used our AI age progression free online tool to see their future selves. Here's what they're saying:",
      // TargetAudienceSection.tsx
      targetAudienceTitle: "Who Can Use Our Face Ager?",
      targetAudienceDescription: "Our face ager is made for everyone. Whether you're young or old, using it alone or with loved ones — our AI age progression free online tool helps you explore the beauty of time.",
      // KeyFeaturesSection.tsx
      keyFeaturesTitle: "Key Features of Our Age Progression Tool",
      keyFeaturesDescription: "Our AI age progression free online tool is built to give you a smooth and fun experience. Here's why people love using it:",
      // HowToGuideSection.tsx
      howToTitle: "How to Use Our AI Age Progression Tool in 3 Simple Steps？",
      howToCta: "Try Age Filter Now",
      // FAQSection.tsx
      faqTitle: "Frequently Asked Questions",
      faqDescription: "Got questions? Here's what to know about using our AI age progression free online tool.",
      // CaseStudySection.tsx
      caseStudyTitle: "Real-World Applications",
      // CompareShowcase.tsx
      compareBefore: "Before",
      compareAfter: "After",
      // data files
      caseStudy1Alt: "AI age progression result of a 7-year-old boy",
      caseStudy2Alt: "AI age progression result of a 20-year-old woman",
      caseStudy3Alt: "AI age progression result of a couple",
      caseStudy4Alt: "AI age progression result of a mom and her daughter",
      caseStudy5Alt: "AI age progression result of friends",
      caseStudy6Alt: "AI age progression result of a man and his dog",
      faq1Question: "What is the meaning of age progression?",
      faq1Answer: "Age progression means showing what a person might look like as they grow older. <a href=\"https://imggen.org\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-400 hover:text-blue-300 underline transition-colors\">Imggen.org</a> - Our AI age progression tool uses facial features and patterns to create a photo aged version of you in seconds. It's like seeing your future self — instantly.",
      faq2Question: "How to get an age progression picture free online?",
      faq2Answer: "It's easy! Just upload your photo to our AI age progression free online tool - <a href=\"https://imggen.org\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-400 hover:text-blue-300 underline transition-colors\">Imggen.org</a>. The aging filter will do the rest. No sign-up, no cost, no watermark — just simple and fast results.",
      faq3Question: "How do you change your age with TikTok old filter?",
      faq3Answer: "On TikTok, people often use the old filter trend. But if you want a more detailed result, try our face aging online free tool - <a href=\"https://imggen.org\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-400 hover:text-blue-300 underline transition-colors\">Imggen.org</a>. It gives you a better look at how your face might age with more natural results.",
      faq4Question: "Is your AI age filter accurate?",
      faq4Answer: "While no tool can predict the future perfectly, our AI age filter is trained to simulate real aging effects — like wrinkles, hair changes, and facial shifts. Many users find the aging filter results surprisingly close to reality.",
      faq5Question: "Will my photos be saved or shared?",
      faq5Answer: "No. We respect your privacy. All photos uploaded to our AI age progression platform are processed securely and deleted shortly after. Your photo aged result is yours only — not stored or shared.",
      howTo1Title: "Upload Your Photo",
      howTo1Description: "Start by uploading a clear portrait to our AI age progression tool. Ensure your face is well-lit and centered. Our smart face ager works best with front-facing images.",
      howTo2Title: "Click \"Generate\" to Age Your Face",
      howTo2Description: "Click the \"Generate\" button to watch how the old person filter transforms your look! Our system will instantly age your photo using advanced AI old and photo ager technology. ",
      howTo3Title: "3. Download Your Future Face",
      howTo3Description: "Preview the result and download your photo aged image in high-quality JPG or PNG format. Try aging your face online free anytime!",
      keyFeature1Title: "Realistic Face Ager",
      keyFeature1Description: "Our age filter and aging filter create real-looking changes — wrinkles, gray hair, and face shape all look natural. Each photo aged result shows what you might truly look like in the future.",
      keyFeature2Title: "No Watermark",
      keyFeature2Description: "You get clean, high-quality images. We never add a watermark to your aged photo — it's yours to keep and share.",
      keyFeature3Title: "Online & Free to Use",
      keyFeature3Description: "No need to download anything. Our AI age progression free online tool runs right in your browser. Try the old age filter, aging filter, or old man filter anytime — 100% free.",
      keyFeature4Title: "Easy and Fast",
      keyFeature4Description: "Just upload a photo, click a button, and see the magic. Anyone can use our age progression tool — no tech skills needed. Whether you're using the face aging online free option or trying the ai aging mode, it's all simple.",
      targetAudience1Title: "See Yourself Age with Mom",
      targetAudience1Description: "Use the age filter on you and your mom's photos. Watch your age progression together and smile at the future.",
      targetAudience2Title: "Age a Child's Photo",
      targetAudience2Description: "Curious about what your child might look like as they grow? Try our AI age progression tool to age a photo of your kid in just seconds.",
      targetAudience3Title: "Grow Old with Your Partner",
      targetAudience3Description: "Use our aging filter on couple photos. See what you and your loved one might look like growing old together. It's fun and touching.",
      testimonial1Name: "Lisa Chen",
      testimonial1Role: "College Student",
      testimonial1Text: "I tried the age filter with my best friend, and we were shocked at how real the results looked. The wrinkles, the hair — everything! The photo aged version was fun to share on social media.",
      testimonial2Name: "David Miller",
      testimonial2Role: "Tech Blogger",
      testimonial2Text: "As someone who tests new tools all the time, this AI age progression free online app really surprised me. It's super fast, very accurate, and there's no watermark. Definitely worth trying!",
      testimonial3Name: "Rosa Martinez",
      testimonial3Role: "Retired Teacher",
      testimonial3Text: "My granddaughter showed me how to use this aging filter. We both uploaded pictures and saw our future faces. It was sweet, a little emotional, and very fun.",
      testimonial4Name: "Emily Zhao",
      testimonial4Role: "High School Student",
      testimonial4Text: "I used the old age filter to make a cool profile picture for my game. Now all my friends are using the face ager too. It's free and works great!",
      testimonial5Name: "Jake Robertson",
      testimonial5Role: "Marketing Manager",
      testimonial5Text: "I used the AI age progression tool for a social media trend. The old man filter made me laugh, but it also looked so realistic. Great way to go viral!",
      testimonial6Name: "Sophie & Alex",
      testimonial6Role: "Newlyweds",
      testimonial6Text: "We used the old person filter on our wedding photos to see what we'll look like in 40 years. The photo aged results were sweet and kind of touching. We even framed one!",
      whyUs1Title: "See Your Older Self in Seconds",
      whyUs1Description: "Wondering what you'll look like when you're older? Our AI age progression free online tool lets you age a photo instantly. No app, no sign-up — just upload and see your future face.",
      whyUs1Button: "Get Your Aged Look",
      whyUs2Title: "Look Aged, Not Edited",
      whyUs2Description: "Forget fake effects. Our age filter and aging filter create smooth, lifelike changes — from fine lines to subtle hair color shifts. The final photo aged version feels real, not overdone.",
      whyUs2Button: "Age Your Photo Now",
      whyUs3Title: "Share It, Trend It",
      whyUs3Description: "Trying the old person filter has gone viral! Join the trend and post your transformation. Our AI age progression tool is perfect for fun social content and group challenges.",
      whyUs3Button: "Age Yourself Now",
      whyUs4Title: "Change Your Look, Stay Private",
      whyUs4Description: "Want to stay low-key online? Use our old filter or old age filter to create a new identity. Great for avatars, private chats, or just having fun with your look.",
      whyUs4Button: "Age Your Face Now"
    }
  },
  de: {
    oldfilter: {
      // German translations
    }
  },
  es: {
    oldfilter: {
      // Spanish translations
    }
  },
  fr: {
    oldfilter: {
      // French translations
    }
  },
  ja: {
    oldfilter: {
      // Japanese translations
    }
  },
  ko: {
    oldfilter: {
      // Korean translations
    }
  },
  pt: {
    oldfilter: {
      // Portuguese translations
    }
  },
  ru: {
    oldfilter: {
      // Russian translations
    }
  },
  th: {
    oldfilter: {
      // Thai translations
    }
  },
  vi: {
    oldfilter: {
      // Vietnamese translations
    }
  },
  "zh-CN": {
    oldfilter: {
      // Simplified Chinese translations
    }
  },
  "zh-HK": {
    oldfilter: {
      // Hong Kong Chinese translations
    }
  },
  "zh-TW": {
    oldfilter: {
      // Taiwan Chinese translations
    }
  }
};

const LOCALES_DIR = path.join(__dirname, './packages/i18n/translations');

function addTranslation() {
  fs.readdirSync(LOCALES_DIR).forEach((file) => {
    if (file.endsWith('.json')) {
      const lang = file.replace('.json', '');
      const filePath = path.join(LOCALES_DIR, file);

      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const existingTranslations = JSON.parse(content);
        const newTranslations = translations[lang] || {};

        // Deep merge objects, new values will overwrite old values
        const updatedTranslations = deepMerge(
          existingTranslations,
          newTranslations
        );

        fs.writeFileSync(
          filePath,
          JSON.stringify(updatedTranslations, null, 2) + '\n',
          'utf8'
        );

        console.log(`✅ Successfully updated ${file}`);
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error);
      }
    }
  });
}

// Helper function for deep merging objects
function deepMerge(target, source) {
  const output = { ...target };

  Object.keys(source).forEach((key) => {
    if (isObject(source[key]) && isObject(target[key])) {
      output[key] = deepMerge(target[key], source[key]);
    } else {
      output[key] = source[key];
    }
  });

  return output;
}

// Helper function to check if an item is an object
function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item);
}

addTranslation();